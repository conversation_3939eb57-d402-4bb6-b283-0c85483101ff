"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Search,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Filter,
  ChevronDown,
  Check,
} from "lucide-react";
import { useAuth } from "../lib/auth-context";
import { apiGet } from "../lib/api";

interface TestHistoryRecord {
  id: string;
  tokenName: string;
  modelName: string;
  testType: "single" | "aggregate";
  success: boolean;
  durationMs: number;
  errorMessage?: string;
  userId: string;
  createdAt: string;
}

interface TestStats {
  totalTests: number;
  successfulTests: number;
  failedTests: number;
  averageDuration: number;
}

export default function TestHistoryPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [history, setHistory] = useState<TestHistoryRecord[]>([]);
  const [stats, setStats] = useState<TestStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<"" | "single" | "aggregate">("");
  const [statusFilter, setStatusFilter] = useState<"" | "success" | "failed">(
    ""
  );
  const [modelFilter, setModelFilter] = useState("");
  const [modelSearchTerm, setModelSearchTerm] = useState("");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Format time for display (already in Malaysia timezone from DB)
  const formatMalaysiaTime = (dateString: string) => {
    // Since we now save Malaysia time directly to DB, just format it nicely
    return new Date(dateString).toLocaleString("en-MY", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  };

  useEffect(() => {
    if (user) {
      fetchTestHistory();
    }
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsModelDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchTestHistory = async () => {
    try {
      setLoading(true);
      const response = await apiGet("/api/test-history");
      const data = await response.json();
      setHistory(data.history || []);
      setStats(data.stats || null);
    } catch (error) {
      console.error("Failed to fetch test history:", error);
    } finally {
      setLoading(false);
    }
  };

  // Get unique models for filter dropdown
  const allUniqueModels = Array.from(
    new Set(history.map((record) => record.modelName))
  ).sort();

  // Filter models based on search term
  const filteredModels = allUniqueModels.filter((model) =>
    model.toLowerCase().includes(modelSearchTerm.toLowerCase())
  );

  const filteredHistory = history.filter((record) => {
    const matchesSearch =
      record.tokenName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.modelName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !typeFilter || record.testType === typeFilter;
    const matchesStatus =
      !statusFilter ||
      (statusFilter === "success" && record.success) ||
      (statusFilter === "failed" && !record.success);

    const matchesModel = !modelFilter || record.modelName === modelFilter;

    return matchesSearch && matchesType && matchesStatus && matchesModel;
  });

  const clearFilters = () => {
    setSearchTerm("");
    setTypeFilter("");
    setStatusFilter("");
    setModelFilter("");
    setModelSearchTerm("");
    setIsModelDropdownOpen(false);
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading test history...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/")}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Tokens
              </Button>
              <h1 className="text-2xl font-semibold text-gray-900">
                Test History
              </h1>
            </div>
            <span className="text-sm text-gray-600">Welcome, {user.name}</span>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Tests
                </CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTests}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Successful
                </CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {stats.successfulTests}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalTests > 0
                    ? (
                        (stats.successfulTests / stats.totalTests) *
                        100
                      ).toFixed(1)
                    : 0}
                  % success rate
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed</CardTitle>
                <XCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {stats.failedTests}
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalTests > 0
                    ? ((stats.failedTests / stats.totalTests) * 100).toFixed(1)
                    : 0}
                  % failure rate
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Duration
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(stats.averageDuration / 1000).toFixed(2)}s
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search by token or model..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-80"
              />
            </div>

            {/* Type Filter */}
            <select
              value={typeFilter}
              onChange={(e) =>
                setTypeFilter(e.target.value as "" | "single" | "aggregate")
              }
              className="flex h-10 w-40 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">All Types</option>
              <option value="single">Single Token</option>
              <option value="aggregate">Aggregate</option>
            </select>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) =>
                setStatusFilter(e.target.value as "" | "success" | "failed")
              }
              className="flex h-10 w-40 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">All Status</option>
              <option value="success">Success</option>
              <option value="failed">Failed</option>
            </select>

            {/* Model Filter with Search */}
            <div
              className="relative w-64"
              ref={dropdownRef}
            >
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search and select a model..."
                value={modelSearchTerm}
                onChange={(e) => {
                  setModelSearchTerm(e.target.value);
                  setIsModelDropdownOpen(true);
                }}
                onFocus={() => setIsModelDropdownOpen(true)}
                className="pl-10"
              />
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />

              {/* Dropdown */}
              {isModelDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-64 overflow-y-auto">
                  {filteredModels.length > 0 ? (
                    filteredModels.map((model) => {
                      const recordCount = history.filter(
                        (record) => record.modelName === model
                      ).length;
                      return (
                        <div
                          key={model}
                          className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 border-b last:border-b-0 ${
                            modelFilter === model ? "bg-blue-50" : ""
                          }`}
                          onClick={() => {
                            setModelFilter(model);
                            setModelSearchTerm(model);
                            setIsModelDropdownOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <span className="font-medium">{model}</span>
                            <Badge variant="secondary">
                              {recordCount} records
                            </Badge>
                          </div>
                          {modelFilter === model && (
                            <Check className="w-4 h-4 text-blue-600" />
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <div className="p-3 text-gray-500 text-center">
                      {allUniqueModels.length === 0
                        ? "No models available"
                        : `No models match "${modelSearchTerm}" (${allUniqueModels.length} total models)`}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Clear Filters */}
            {(searchTerm ||
              typeFilter ||
              statusFilter ||
              modelFilter ||
              modelSearchTerm) && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-gray-600"
              >
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* Test History List */}
        <div className="space-y-4">
          {filteredHistory.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <p className="text-gray-500">No test history found.</p>
              </CardContent>
            </Card>
          ) : (
            filteredHistory.map((record) => (
              <Card key={record.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {record.success ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                        <Badge
                          variant={record.success ? "default" : "destructive"}
                          className="text-xs"
                        >
                          {record.success ? "Success" : "Failed"}
                        </Badge>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {record.tokenName} → {record.modelName}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {record.testType === "single"
                            ? "Single Token Test"
                            : "Aggregate Test"}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {(record.durationMs / 1000).toFixed(2)}s
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatMalaysiaTime(record.createdAt)}
                      </div>
                    </div>
                  </div>
                  {record.errorMessage && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-700">
                        {record.errorMessage}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </main>
    </div>
  );
}
