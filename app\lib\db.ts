import Database from "better-sqlite3"
import path from "path"
import fs from "fs"

const dataDir = path.join(process.cwd(), "data")
const dbPath = path.join(dataDir, "tokens.db")

// Ensure data directory exists
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}

const db = new Database(dbPath)

// Initialize database
db.exec(`
  CREATE TABLE IF NOT EXISTS tokens (
    name TEXT PRIMARY KEY,
    type TEXT NOT NULL DEFAULT 'Chat API',
    url_path TEXT,
    api_path TEXT,
    token TEXT,
    model_list TEXT,
    note TEXT,
    post_url TEXT,
    status TEXT DEFAULT 'not tested',
    can_fetch BOOLEAN DEFAULT 0,
    last_test_success BOOLEAN,
    last_test_duration INTEGER,
    last_test_at DATETIME,
    user_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`)

// Create test history table
db.exec(`
  CREATE TABLE IF NOT EXISTS test_history (
    id TEXT PRIMARY KEY,
    token_name TEXT NOT NULL,
    model_name TEXT NOT NULL,
    test_type TEXT NOT NULL CHECK (test_type IN ('single', 'aggregate')),
    success BOOLEAN NOT NULL,
    duration_ms INTEGER NOT NULL,
    error_message TEXT,
    user_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (token_name) REFERENCES tokens (name) ON DELETE CASCADE
  )
`)

// Add new columns if they don't exist (for existing databases)
try {
  db.exec(`ALTER TABLE tokens ADD COLUMN status TEXT DEFAULT 'not tested'`)
} catch (e) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN can_fetch BOOLEAN DEFAULT 0`)
} catch (e) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN type TEXT NOT NULL DEFAULT 'Chat API'`)
} catch (error) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN last_test_success BOOLEAN`)
} catch (error) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN last_test_duration INTEGER`)
} catch (error) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN last_test_at DATETIME`)
} catch (error) {
  // Column already exists
}

export interface TokenModelTestResult {
  modelName: string
  lastTestSuccess?: boolean
  lastTestDuration?: number
  lastTestAt?: string
}

export interface TokenRecord {
  name: string
  type: 'Chat API' | 'Claude Code'
  urlPath?: string
  apiPath?: string
  token?: string
  modelList?: string[]
  note?: string
  postUrl?: string
  status?: 'active' | 'failed' | 'not tested' | 'test again'
  canFetch?: boolean
  lastTestSuccess?: boolean
  lastTestDuration?: number
  lastTestAt?: string
  userId?: string
  modelTestResults?: TokenModelTestResult[]
}

export interface TestHistoryRecord {
  id: string
  tokenName: string
  modelName: string
  testType: 'single' | 'aggregate'
  success: boolean
  durationMs: number
  errorMessage?: string
  userId: string
  createdAt: string
}

export class TokenService {
  static getAllTokens(userId: string, search?: string, typeFilter?: string): TokenRecord[] {
    let query = "SELECT * FROM tokens WHERE user_id = ?"
    const params: any[] = [userId]

    if (typeFilter) {
      query += " AND type = ?"
      params.push(typeFilter)
    }

    if (search) {
      query += ` AND (
        name LIKE ? OR
        type LIKE ? OR
        url_path LIKE ? OR
        api_path LIKE ? OR
        token LIKE ? OR
        model_list LIKE ? OR
        note LIKE ? OR
        post_url LIKE ?
      )`
      const searchPattern = `%${search}%`
      params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }

    query += " ORDER BY created_at DESC"

    const stmt = db.prepare(query)
    const rows = stmt.all(...params) as any[]

    return rows.map((row) => {
      const token = {
        name: row.name,
        type: row.type || 'Chat API',
        urlPath: row.url_path,
        apiPath: row.api_path,
        token: row.token,
        modelList: row.model_list ? JSON.parse(row.model_list) : [],
        note: row.note,
        postUrl: row.post_url,
        status: row.status || 'not tested',
        canFetch: Boolean(row.can_fetch),
        lastTestSuccess: row.last_test_success !== null ? Boolean(row.last_test_success) : undefined,
        lastTestDuration: row.last_test_duration || undefined,
        lastTestAt: row.last_test_at || undefined,
        modelTestResults: this.getTokenModelTestResults(row.name, row.user_id)
      }
      return token
    })
  }

  static createToken(token: TokenRecord, userId: string): string {
    const stmt = db.prepare(`
      INSERT INTO tokens (name, type, url_path, api_path, token, model_list, note, post_url, status, can_fetch, user_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      token.name,
      token.type,
      token.urlPath || null,
      token.apiPath || null,
      token.token || null,
      token.modelList ? JSON.stringify(token.modelList) : null,
      token.note || null,
      token.postUrl || null,
      token.status || 'not tested',
      token.canFetch ? 1 : 0,
      userId,
    )

    return token.name
  }

  static updateToken(name: string, token: TokenRecord, userId: string): boolean {
    const stmt = db.prepare(`
      UPDATE tokens
      SET type = ?, url_path = ?, api_path = ?, token = ?, model_list = ?, note = ?, post_url = ?, status = ?, can_fetch = ?, updated_at = CURRENT_TIMESTAMP
      WHERE name = ? AND user_id = ?
    `)

    const result = stmt.run(
      token.type,
      token.urlPath || null,
      token.apiPath || null,
      token.token || null,
      token.modelList ? JSON.stringify(token.modelList) : null,
      token.note || null,
      token.postUrl || null,
      token.status || 'not tested',
      token.canFetch ? 1 : 0,
      name,
      userId,
    )

    return result.changes > 0
  }

  static deleteToken(name: string, userId: string): boolean {
    const stmt = db.prepare("DELETE FROM tokens WHERE name = ? AND user_id = ?")
    const result = stmt.run(name, userId)
    return result.changes > 0
  }

  static updateLastTestResult(tokenName: string, success: boolean, durationMs: number, userId: string): boolean {
    // Use current time in Malaysia timezone
    const malaysiaTime = new Date().toLocaleString("sv-SE", {
      timeZone: "Asia/Kuala_Lumpur"
    })

    const stmt = db.prepare(`
      UPDATE tokens
      SET last_test_success = ?, last_test_duration = ?, last_test_at = ?, updated_at = ?
      WHERE name = ? AND user_id = ?
    `)
    const result = stmt.run(success ? 1 : 0, durationMs, malaysiaTime, malaysiaTime, tokenName, userId)
    return result.changes > 0
  }

  static getTokenModelTestResults(tokenName: string, userId: string): TokenModelTestResult[] {
    const stmt = db.prepare(`
      SELECT
        model_name,
        success,
        duration_ms,
        created_at
      FROM test_history
      WHERE token_name = ? AND user_id = ?
      ORDER BY created_at DESC
    `)

    const rows = stmt.all(tokenName, userId) as any[]
    const modelResults: { [key: string]: TokenModelTestResult } = {}

    // Get the latest test result for each model
    rows.forEach(row => {
      if (!modelResults[row.model_name]) {
        modelResults[row.model_name] = {
          modelName: row.model_name,
          lastTestSuccess: Boolean(row.success),
          lastTestDuration: row.duration_ms,
          lastTestAt: row.created_at
        }
      }
    })

    return Object.values(modelResults)
  }
}

export class TestHistoryService {
  static createTestRecord(record: Omit<TestHistoryRecord, 'id' | 'createdAt'>): string {
    const id = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    // Use current time in Malaysia timezone for created_at
    const malaysiaTime = new Date().toLocaleString("sv-SE", {
      timeZone: "Asia/Kuala_Lumpur"
    })

    const stmt = db.prepare(`
      INSERT INTO test_history (id, token_name, model_name, test_type, success, duration_ms, error_message, user_id, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      id,
      record.tokenName,
      record.modelName,
      record.testType,
      record.success ? 1 : 0,
      record.durationMs,
      record.errorMessage || null,
      record.userId,
      malaysiaTime
    )

    return id
  }

  static getTestHistory(userId: string, tokenName?: string, limit: number = 50): TestHistoryRecord[] {
    let query = "SELECT * FROM test_history WHERE user_id = ?"
    const params: any[] = [userId]

    if (tokenName) {
      query += " AND token_name = ?"
      params.push(tokenName)
    }

    query += " ORDER BY created_at DESC LIMIT ?"
    params.push(limit)

    const stmt = db.prepare(query)
    const rows = stmt.all(...params) as any[]

    return rows.map((row) => ({
      id: row.id,
      tokenName: row.token_name,
      modelName: row.model_name,
      testType: row.test_type,
      success: Boolean(row.success),
      durationMs: row.duration_ms,
      errorMessage: row.error_message,
      userId: row.user_id,
      createdAt: row.created_at,
    }))
  }

  static getTestStats(userId: string, tokenName?: string) {
    let query = "SELECT COUNT(*) as total, SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful, AVG(duration_ms) as avgDuration FROM test_history WHERE user_id = ?"
    const params: any[] = [userId]

    if (tokenName) {
      query += " AND token_name = ?"
      params.push(tokenName)
    }

    const stmt = db.prepare(query)
    const result = stmt.get(...params) as any

    return {
      totalTests: result.total || 0,
      successfulTests: result.successful || 0,
      failedTests: (result.total || 0) - (result.successful || 0),
      averageDuration: result.avgDuration || 0
    }
  }

  static getTestStats(userId: string, tokenName?: string): {
    totalTests: number
    successfulTests: number
    failedTests: number
    averageDuration: number
  } {
    let query = "SELECT COUNT(*) as total, SUM(success) as successful, AVG(duration_ms) as avg_duration FROM test_history WHERE user_id = ?"
    const params: any[] = [userId]

    if (tokenName) {
      query += " AND token_name = ?"
      params.push(tokenName)
    }

    const stmt = db.prepare(query)
    const result = stmt.get(...params) as any

    return {
      totalTests: result.total || 0,
      successfulTests: result.successful || 0,
      failedTests: (result.total || 0) - (result.successful || 0),
      averageDuration: Math.round(result.avg_duration || 0),
    }
  }
}

export default db
