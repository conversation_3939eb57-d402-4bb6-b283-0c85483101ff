"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  LogOut,
  X,
  Copy,
  Eye,
  EyeOff,
  RotateCcw,
  Globe,
  ChevronDown,
  Check,
  History,
} from "lucide-react";
import { useAuth } from "./lib/auth-context";
import { apiGet, apiPost, apiPut, apiDelete } from "./lib/api";
import { useRouter } from "next/navigation";

interface TokenModelTestResult {
  modelName: string;
  lastTestSuccess?: boolean;
  lastTestDuration?: number;
  lastTestAt?: string;
}

interface TokenRecord {
  name: string;
  type: "Chat API" | "Claude Code";
  urlPath?: string;
  apiPath?: string;
  token?: string;
  modelList?: string[];
  note?: string;
  postUrl?: string;
  status?: "active" | "failed" | "not tested" | "test again";
  canFetch?: boolean;
  lastTestSuccess?: boolean;
  lastTestDuration?: number;
  lastTestAt?: string;
  modelTestResults?: TokenModelTestResult[];
}

export default function HomePage() {
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  const [tokens, setTokens] = useState<TokenRecord[]>([]);
  const hasInitializedRef = useRef(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTokenName, setEditingTokenName] = useState<string | null>(null);
  const [isFetching, setIsFetching] = useState(false);
  const [fetchMessage, setFetchMessage] = useState<string>("");
  const [newModelInput, setNewModelInput] = useState("");
  const [showTokens, setShowTokens] = useState<{ [key: string]: boolean }>({});
  const [expandedModels, setExpandedModels] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [modelFilterSearchTerm, setModelFilterSearchTerm] =
    useState<string>("");
  const [isModelFilterDropdownOpen, setIsModelFilterDropdownOpen] =
    useState(false);
  const modelFilterDropdownRef = useRef<HTMLDivElement>(null);
  const [copiedText, setCopiedText] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [currentToken, setCurrentToken] = useState<TokenRecord>({
    name: "",
    type: "Chat API",
    urlPath: "",
    apiPath: "",
    token: "",
    modelList: [],
    note: "",
    postUrl: "",
    status: "not tested",
    canFetch: false,
  });
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [testingToken, setTestingToken] = useState<TokenRecord | null>(null);
  const [testSelectedModels, setTestSelectedModels] = useState<string[]>([]);
  const [modelSearchTerm, setModelSearchTerm] = useState<string>("");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [isTestLoading, setIsTestLoading] = useState(false);
  const [testResults, setTestResults] = useState<{
    [model: string]: {
      success: boolean;
      response: string;
      error?: string;
      firstByteTime?: number;
      totalTime?: number;
    };
  }>({});
  const [currentTestingModel, setCurrentTestingModel] = useState<string>("");

  // Delete confirmation states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tokenToDelete, setTokenToDelete] = useState<string | null>(null);

  const fetchTokens = useCallback(async (search?: string, type?: string) => {
    try {
      const params = new URLSearchParams();
      if (search) params.append("search", search);
      if (type) params.append("type", type);

      const url = `/api/tokens${
        params.toString() ? "?" + params.toString() : ""
      }`;
      const response = await apiGet(url);
      if (response.ok) {
        const data = await response.json();
        setTokens(data);
      }
    } catch (error) {
      console.error("Failed to fetch tokens:", error);
    }
  }, []);

  // Handle URL error parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get("error");

    if (error) {
      let errorMsg = "";
      switch (error) {
        case "no_code":
          errorMsg =
            "Authentication failed: No authorization code received from Keycloak. Please try logging in again.";
          break;
        case "auth_failed":
          errorMsg =
            "Authentication failed: Unable to complete the login process. Please try again.";
          break;
        default:
          errorMsg = `Authentication error: ${error}`;
      }
      setErrorMessage(errorMsg);

      // Clear the error from URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, "", newUrl);

      // Clear error message after 10 seconds
      setTimeout(() => setErrorMessage(""), 10000);
    }
  }, []);

  useEffect(() => {
    if (!user) {
      // Reset initialization flag when user logs out
      hasInitializedRef.current = false;
    } else if (user && !hasInitializedRef.current) {
      // Initialize tokens when user is authenticated for the first time
      hasInitializedRef.current = true;
      fetchTokens();
    }
  }, [user, fetchTokens]);

  const handleSave = async () => {
    setIsLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const url = editingTokenName
        ? `/api/tokens/${editingTokenName}`
        : "/api/tokens";

      const response = editingTokenName
        ? await apiPut(url, currentToken)
        : await apiPost(url, currentToken);

      if (response.ok) {
        const action = editingTokenName ? "updated" : "created";
        setSuccessMessage(
          `Token "${currentToken.name}" ${action} successfully!`
        );

        fetchTokens();
        setIsAddDialogOpen(false);
        setIsEditDialogOpen(false);
        setEditingTokenName(null);
        setCurrentToken({
          name: "",
          type: "Chat API",
          urlPath: "",
          apiPath: "",
          token: "",
          modelList: [],
          note: "",
          postUrl: "",
          status: "not tested",
          canFetch: false,
        });
        setFetchMessage("");
        setNewModelInput("");

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(""), 3000);
      } else {
        const errorData = await response.json();
        setErrorMessage(errorData.error || "Failed to save token");
      }
    } catch (error) {
      console.error("Failed to save token:", error);
      setErrorMessage("Network error occurred while saving token");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (name: string) => {
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const response = await apiDelete(`/api/tokens/${name}`);

      if (response.ok) {
        setSuccessMessage(`Token "${name}" deleted successfully!`);
        fetchTokens();

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(""), 3000);
      } else {
        const errorData = await response.json();
        setErrorMessage(errorData.error || "Failed to delete token");
      }
    } catch (error) {
      console.error("Failed to delete token:", error);
      setErrorMessage("Network error occurred while deleting token");
    }
  };

  const handleDeleteClick = (name: string) => {
    setTokenToDelete(name);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (tokenToDelete) {
      setDeleteDialogOpen(false);
      await handleDelete(tokenToDelete);
      setTokenToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setTokenToDelete(null);
  };

  const handleEdit = (token: TokenRecord) => {
    setCurrentToken(token);
    setEditingTokenName(token.name);
    setFetchMessage("");
    setErrorMessage("");
    setSuccessMessage("");
    setIsEditDialogOpen(true);
  };

  const addModel = () => {
    if (
      newModelInput.trim() &&
      !currentToken.modelList?.includes(newModelInput.trim())
    ) {
      setCurrentToken({
        ...currentToken,
        modelList: [...(currentToken.modelList || []), newModelInput.trim()],
      });
      setNewModelInput("");
    }
  };

  const removeModel = (modelToRemove: string) => {
    setCurrentToken({
      ...currentToken,
      modelList:
        currentToken.modelList?.filter((model) => model !== modelToRemove) ||
        [],
    });
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(""), 2000); // Clear after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // Get model test status for badge styling
  const getModelTestStatus = (token: TokenRecord, modelName: string) => {
    const modelResult = token.modelTestResults?.find(
      (result) => result.modelName === modelName
    );
    return modelResult?.lastTestSuccess;
  };

  // Format time for display (already in Malaysia timezone from DB)
  const formatMalaysiaTime = (dateString: string) => {
    // Since we now save Malaysia time directly to DB, just format it nicely
    return new Date(dateString).toLocaleString("en-MY", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  };

  const toggleTokenVisibility = (tokenName: string) => {
    setShowTokens((prev) => ({
      ...prev,
      [tokenName]: !prev[tokenName],
    }));
  };

  const formatToken = (
    token: string,
    tokenName: string,
    isVisible: boolean
  ) => {
    if (!token) return "";
    if (isVisible) return token;
    if (token.length <= 10) return token;
    return `${token.substring(0, 5)}****${token.substring(token.length - 5)}`;
  };

  const getApiPath = (urlPath?: string) => {
    if (!urlPath) return "";
    let cleanPath = urlPath.trim();
    // Remove trailing slash to avoid double slashes
    cleanPath = cleanPath.replace(/\/+$/, "");
    if (cleanPath.startsWith("http://") || cleanPath.startsWith("https://")) {
      return `${cleanPath}/v1/`;
    }
    return `https://${cleanPath}/v1/`;
  };

  const toggleModelsExpansion = (tokenName: string) => {
    setExpandedModels((prev) => ({
      ...prev,
      [tokenName]: !prev[tokenName],
    }));
  };

  // Get all unique models from all tokens
  const getAllModels = () => {
    const allModels = new Set<string>();
    tokens.forEach((token) => {
      token.modelList?.forEach((model) => allModels.add(model));
    });
    return Array.from(allModels).sort();
  };

  // Get all unique statuses
  const getAllStatuses = () => {
    const statuses = ["active", "failed", "not tested", "test again"];
    return statuses;
  };

  const handleFetchModels = async () => {
    if (!currentToken.urlPath || !currentToken.token) {
      setFetchMessage("URL Path and Token are required to fetch models");
      return;
    }

    setIsFetching(true);
    setFetchMessage("");

    try {
      const response = await apiPost("/api/tokens/fetch-models", {
        urlPath: currentToken.urlPath,
        token: currentToken.token,
      });

      const result = await response.json();

      if (result.success) {
        setCurrentToken({
          ...currentToken,
          modelList: result.models,
          canFetch: true,
        });
        setFetchMessage(`✅ ${result.message}`);
      } else {
        setCurrentToken({
          ...currentToken,
          canFetch: false,
        });
        setFetchMessage(`❌ ${result.error}`);
      }
    } catch (error) {
      setCurrentToken({
        ...currentToken,
        canFetch: false,
      });
      setFetchMessage("❌ Network error occurred while fetching models");
    } finally {
      setIsFetching(false);
    }
  };

  const handleAdd = () => {
    setCurrentToken({
      name: "",
      type: "Chat API",
      urlPath: "",
      apiPath: "",
      token: "",
      modelList: [],
      note: "",
      postUrl: "",
      status: "not tested",
      canFetch: false,
    });
    setEditingTokenName(null);
    setFetchMessage("");
    setErrorMessage("");
    setSuccessMessage("");
    setIsAddDialogOpen(true);
  };

  const handleResetFilters = () => {
    setSearchTerm("");
    setTypeFilter("");
    setSelectedModel("");
    setSelectedStatus("");
    setModelFilterSearchTerm("");
    setIsModelFilterDropdownOpen(false);
  };

  const handleTest = (token: TokenRecord) => {
    setTestingToken(token);
    setTestSelectedModels([]);
    setModelSearchTerm("");
    setIsModelDropdownOpen(false);
    setTestResults({});
    setCurrentTestingModel("");
    setIsTestDialogOpen(true);
  };

  const recordTestResult = async (
    tokenName: string,
    modelName: string,
    success: boolean,
    durationMs: number,
    errorMessage?: string
  ) => {
    try {
      await apiPost("/api/test-history", {
        tokenName,
        modelName,
        testType: "single",
        success,
        durationMs,
        errorMessage,
      });
    } catch (error) {
      console.error("Failed to record test result:", error);
    }
  };

  const handleRunTest = async () => {
    if (!testingToken || testSelectedModels.length === 0) {
      return;
    }

    setIsTestLoading(true);
    setTestResults({});

    // Test each selected model
    for (const model of testSelectedModels) {
      setCurrentTestingModel(model);

      const startTime = performance.now();
      let firstByteTime: number | undefined;
      let totalTime: number | undefined;

      try {
        // Construct the API URL based on token configuration
        let apiUrl = "/api/chat"; // Default fallback

        if (testingToken.postUrl) {
          // If postUrl is provided, use it directly
          apiUrl = testingToken.postUrl;
        } else if (testingToken.urlPath) {
          // If urlPath is provided, append /v1/chat/completions (handle trailing slashes)
          const baseUrl = testingToken.urlPath.replace(/\/+$/, "");
          apiUrl = `${baseUrl}/v1/chat/completions`;
        } else if (testingToken.apiPath) {
          // If only apiPath is provided, use it as relative path
          apiUrl = testingToken.apiPath;
        }

        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        // Add authorization header if token is provided
        if (testingToken.token) {
          headers["Authorization"] = `Bearer ${testingToken.token}`;
        }

        const requestBody = {
          model: model,
          messages: [{ role: "user", content: "你好" }],
        };

        const response = await fetch(apiUrl, {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody),
        });

        // Record first byte time (when response headers are received)
        firstByteTime = performance.now() - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          totalTime = performance.now() - startTime;
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText };
          }
          throw new Error(
            errorData.error || `HTTP ${response.status}: ${response.statusText}`
          );
        }

        const data = await response.json();
        // Record total time (when response is fully received and parsed)
        totalTime = performance.now() - startTime;

        setTestResults((prev) => ({
          ...prev,
          [model]: {
            success: true,
            response: JSON.stringify(data, null, 2),
            firstByteTime,
            totalTime,
          },
        }));

        // Record successful test result
        await recordTestResult(testingToken.name, model, true, totalTime || 0);

        // Update token's last test result
        try {
          await apiPost("/api/tokens/update-test-result", {
            tokenName: testingToken.name,
            success: true,
            durationMs: totalTime || 0,
          });
        } catch (error) {
          console.error("Failed to update token test result:", error);
        }
      } catch (err) {
        totalTime = performance.now() - startTime;
        const errorMessage = err instanceof Error ? err.message : "Test failed";
        setTestResults((prev) => ({
          ...prev,
          [model]: {
            success: false,
            response: "",
            error: errorMessage,
            firstByteTime,
            totalTime,
          },
        }));

        // Record failed test result
        await recordTestResult(
          testingToken.name,
          model,
          false,
          totalTime || 0,
          errorMessage
        );

        // Update token's last test result
        try {
          await apiPost("/api/tokens/update-test-result", {
            tokenName: testingToken.name,
            success: false,
            durationMs: totalTime || 0,
          });
        } catch (error) {
          console.error("Failed to update token test result:", error);
        }
      }
    }

    setCurrentTestingModel("");
    setIsTestLoading(false);

    // Refresh tokens to show updated test results
    await fetchTokens();
  };

  const toggleModelSelection = (model: string) => {
    setTestSelectedModels((prev) =>
      prev.includes(model) ? prev.filter((m) => m !== model) : [...prev, model]
    );
  };

  const getFilteredModels = () => {
    if (!testingToken?.modelList) return [];
    return testingToken.modelList.filter((model) =>
      model.toLowerCase().includes(modelSearchTerm.toLowerCase())
    );
  };

  // Get filtered models for main page filter
  const getFilteredModelsForFilter = () => {
    const allModels = getAllModels();
    return allModels.filter((model) =>
      model.toLowerCase().includes(modelFilterSearchTerm.toLowerCase())
    );
  };

  // Use debounced search and type filter
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (user) {
        fetchTokens(searchTerm || undefined, typeFilter || undefined);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, typeFilter, user, fetchTokens]);

  // Close model filter dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modelFilterDropdownRef.current &&
        !modelFilterDropdownRef.current.contains(event.target as Node)
      ) {
        setIsModelFilterDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const filteredTokens = tokens.filter((token) => {
    // Model filter (client-side for UI)
    const matchesModel =
      !selectedModel || token.modelList?.includes(selectedModel);

    // Status filter (client-side for UI)
    const matchesStatus =
      !selectedStatus || (token.status || "not tested") === selectedStatus;

    return matchesModel && matchesStatus;
  });

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login page if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-xl sm:text-2xl">Token Manager</CardTitle>
            <CardDescription>Please log in to continue</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="w-full"
              onClick={() => (window.location.href = "/api/auth/login")}
            >
              Login with Keycloak
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center h-auto sm:h-16 py-4 sm:py-0 gap-4 sm:gap-0">
            <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">
              Token Manager
            </h1>
            <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 w-full sm:w-auto">
              <div className="flex flex-row gap-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/aggregate-test")}
                  className="text-purple-600 hover:text-purple-700 border-purple-200 hover:border-purple-300 flex-1 sm:flex-none"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Aggregate Test</span>
                  <span className="sm:hidden">Aggregate</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/test-history")}
                  className="text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300 flex-1 sm:flex-none"
                >
                  <History className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Test History</span>
                  <span className="sm:hidden">History</span>
                </Button>
              </div>
              <span className="text-sm text-gray-600 hidden md:inline">
                Welcome, {user.name}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                className="w-full sm:w-auto"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto">
            <div className="relative w-full sm:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search tokens..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              {/* Type Filter */}
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="flex h-10 w-full sm:w-40 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="">All Types</option>
                <option value="Chat API">Chat API</option>
                <option value="Claude Code">Claude Code</option>
              </select>

              {/* Model Filter with Search */}
              <div
                className="relative w-full sm:w-64"
                ref={modelFilterDropdownRef}
              >
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search and select a model..."
                  value={modelFilterSearchTerm}
                  onChange={(e) => {
                    setModelFilterSearchTerm(e.target.value);
                    setIsModelFilterDropdownOpen(true);
                  }}
                  onFocus={() => setIsModelFilterDropdownOpen(true)}
                  className="pl-10"
                />
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />

                {/* Dropdown */}
                {isModelFilterDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-64 overflow-y-auto">
                    {getFilteredModelsForFilter().length > 0 ? (
                      getFilteredModelsForFilter().map((model) => {
                        return (
                          <div
                            key={model}
                            className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 border-b last:border-b-0 ${
                              selectedModel === model ? "bg-blue-50" : ""
                            }`}
                            onClick={() => {
                              setSelectedModel(model);
                              setModelFilterSearchTerm(model);
                              setIsModelFilterDropdownOpen(false);
                            }}
                          >
                            <div className="flex items-center gap-3">
                              <span className="font-medium">{model}</span>
                              <Badge variant="secondary">
                                {
                                  tokens.filter((token) =>
                                    token.modelList?.includes(model)
                                  ).length
                                }{" "}
                                tokens
                              </Badge>
                            </div>
                            {selectedModel === model && (
                              <Check className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <div className="p-3 text-gray-500 text-center">
                        {getAllModels().length === 0
                          ? `No models available (${tokens.length} tokens loaded)`
                          : `No models match "${modelFilterSearchTerm}" (${
                              getAllModels().length
                            } total models)`}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Status Filter */}
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="flex h-10 w-full sm:w-40 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="">All Status</option>
                {getAllStatuses().map((status) => (
                  <option
                    key={status}
                    value={status}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>

              {/* Reset Filters Button */}
              {(searchTerm ||
                typeFilter ||
                selectedModel ||
                selectedStatus ||
                modelFilterSearchTerm) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetFilters}
                  className="h-10 w-full sm:w-auto"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Reset
                </Button>
              )}
            </div>
          </div>
          <Button
            onClick={handleAdd}
            className="w-full lg:w-auto"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Token
          </Button>
        </div>

        <div className="grid gap-4">
          {filteredTokens.map((token) => (
            <Card
              key={token.name}
              className="hover:shadow-md transition-shadow"
            >
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
                  <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                        <h3 className="text-lg font-semibold">{token.name}</h3>
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium w-fit ${
                            token.type === "Chat API"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-purple-100 text-purple-800"
                          }`}
                        >
                          {token.type}
                        </span>
                      </div>
                      <div className="flex flex-wrap items-center gap-2">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            token.status === "active"
                              ? "bg-green-100 text-green-800"
                              : token.status === "failed"
                              ? "bg-red-100 text-red-800"
                              : token.status === "test again"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          {(token.status || "not tested")
                            .split(" ")
                            .map(
                              (word) =>
                                word.charAt(0).toUpperCase() + word.slice(1)
                            )
                            .join(" ")}
                        </span>
                        {token.canFetch && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Can Fetch
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col lg:flex-row lg:gap-6 text-sm space-y-4 lg:space-y-0">
                      {/* Left side: URL Path, API Path, Post URL */}
                      <div className="flex-1 space-y-2">
                        {token.urlPath && (
                          <div>
                            <span className="font-medium text-gray-600">
                              URL Path:
                            </span>
                            <div className="mt-1 flex items-start gap-2">
                              <span className="text-gray-900 break-all flex-1">
                                {token.urlPath}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 flex-shrink-0"
                                onClick={() =>
                                  copyToClipboard(token.urlPath || "")
                                }
                              >
                                {copiedText === token.urlPath ? (
                                  <span className="text-green-600 text-xs">
                                    ✓
                                  </span>
                                ) : (
                                  <Copy className="h-3 w-3" />
                                )}
                              </Button>
                            </div>
                          </div>
                        )}
                        {token.urlPath && (
                          <div>
                            <span className="font-medium text-gray-600">
                              API Path:
                            </span>
                            <div className="mt-1 flex items-start gap-2">
                              <span className="text-gray-900 break-all flex-1">
                                {getApiPath(token.urlPath)}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 flex-shrink-0"
                                onClick={() =>
                                  copyToClipboard(getApiPath(token.urlPath))
                                }
                              >
                                {copiedText === getApiPath(token.urlPath) ? (
                                  <span className="text-green-600 text-xs">
                                    ✓
                                  </span>
                                ) : (
                                  <Copy className="h-3 w-3" />
                                )}
                              </Button>
                            </div>
                          </div>
                        )}
                        {token.postUrl && (
                          <div>
                            <span className="font-medium text-gray-600">
                              Post URL:
                            </span>
                            <div className="mt-1 flex items-start gap-2">
                              <span className="text-gray-900 break-all flex-1">
                                {token.postUrl}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 flex-shrink-0"
                                onClick={() =>
                                  copyToClipboard(token.postUrl || "")
                                }
                              >
                                {copiedText === token.postUrl ? (
                                  <span className="text-green-600 text-xs">
                                    ✓
                                  </span>
                                ) : (
                                  <Copy className="h-3 w-3" />
                                )}
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Right side: Note */}
                      {token.note && (
                        <div className="flex-1 text-sm">
                          <span className="font-medium text-gray-600">
                            Note:
                          </span>
                          <p className="text-gray-900 mt-1">{token.note}</p>
                        </div>
                      )}
                    </div>

                    {token.token && (
                      <div className="text-sm">
                        <span className="font-medium text-gray-600">
                          Token:
                        </span>
                        <div className="mt-1 bg-gray-100 p-2 rounded inline-block">
                          <span className="text-gray-900 font-mono text-xs">
                            {formatToken(
                              token.token,
                              token.name,
                              showTokens[token.name] || false
                            )}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0 ml-1 inline-flex align-middle"
                            onClick={() => toggleTokenVisibility(token.name)}
                          >
                            {showTokens[token.name] ? (
                              <EyeOff className="h-3 w-3" />
                            ) : (
                              <Eye className="h-3 w-3" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0 ml-1 inline-flex align-middle"
                            onClick={() => copyToClipboard(token.token || "")}
                          >
                            {copiedText === token.token ? (
                              <span className="text-green-600 text-xs">✓</span>
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Model List at the bottom - expandable display */}
                    {token.modelList && token.modelList.length > 0 && (
                      <div className="text-sm">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-gray-600">
                            Models ({token.modelList.length}):
                          </span>
                          {token.modelList.length > 6 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={() => toggleModelsExpansion(token.name)}
                            >
                              {expandedModels[token.name]
                                ? "Show Less"
                                : "Show All"}
                            </Button>
                          )}
                        </div>
                        <div
                          className={`flex flex-wrap gap-1 ${
                            !expandedModels[token.name]
                              ? "max-h-16 overflow-hidden"
                              : ""
                          }`}
                        >
                          {token.modelList.map((model, index) => {
                            const testStatus = getModelTestStatus(token, model);
                            return (
                              <Badge
                                key={index}
                                variant="secondary"
                                className={`text-xs ${
                                  testStatus === true
                                    ? "bg-green-100 text-green-800 border-green-200"
                                    : testStatus === false
                                    ? "bg-red-100 text-red-800 border-red-200"
                                    : ""
                                }`}
                              >
                                {model}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 lg:ml-4 w-full lg:w-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTest(token)}
                      className="text-blue-600 hover:text-blue-700 w-full sm:w-auto"
                      title="Test API"
                    >
                      <Globe className="w-4 h-4 mr-2 sm:mr-0" />
                      <span className="sm:hidden">Test API</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(token)}
                      className="w-full sm:w-auto"
                    >
                      <Edit className="w-4 h-4 mr-2 sm:mr-0" />
                      <span className="sm:hidden">Edit</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteClick(token.name)}
                      className="text-red-600 hover:text-red-700 w-full sm:w-auto"
                    >
                      <Trash2 className="w-4 h-4 mr-2 sm:mr-0" />
                      <span className="sm:hidden">Delete</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTokens.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No tokens found</p>
          </div>
        )}
      </main>

      {/* Add/Edit Dialog */}
      <Dialog
        open={isAddDialogOpen || isEditDialogOpen}
        onOpenChange={(open) => {
          setIsAddDialogOpen(false);
          setIsEditDialogOpen(false);
          setNewModelInput("");
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-full">
          <DialogHeader>
            <DialogTitle>
              {editingTokenName ? "Edit Token" : "Add New Token"}
            </DialogTitle>
            <DialogDescription>
              {editingTokenName
                ? "Update the token information"
                : "Create a new token record"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={currentToken.name}
                  onChange={(e) =>
                    setCurrentToken({ ...currentToken, name: e.target.value })
                  }
                  disabled={editingTokenName !== null}
                />
              </div>
              <div>
                <Label htmlFor="type">Type</Label>
                <select
                  id="type"
                  value={currentToken.type}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      type: e.target.value as "Chat API" | "Claude Code",
                    })
                  }
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                >
                  <option value="Chat API">Chat API</option>
                  <option value="Claude Code">Claude Code</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="urlPath">URL Path</Label>
                <Input
                  id="urlPath"
                  value={currentToken.urlPath || ""}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      urlPath: e.target.value,
                    })
                  }
                  placeholder="e.g., https://api.example.com"
                />
              </div>
              <div>
                <Label htmlFor="apiPath">API Path</Label>
                <Input
                  id="apiPath"
                  value={currentToken.apiPath || ""}
                  onChange={(e) =>
                    setCurrentToken({
                      ...currentToken,
                      apiPath: e.target.value,
                    })
                  }
                  placeholder={
                    currentToken.type === "Claude Code"
                      ? "Required for Claude Code"
                      : "e.g., /v1/chat/completions"
                  }
                />
              </div>
            </div>

            <div>
              <Label htmlFor="token">Token</Label>
              <Input
                id="token"
                value={currentToken.token || ""}
                onChange={(e) =>
                  setCurrentToken({ ...currentToken, token: e.target.value })
                }
                className="font-mono text-sm"
                placeholder="Enter your API token..."
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Left side: Post URL and Status (stacked) */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="postUrl">Post URL</Label>
                  <Input
                    id="postUrl"
                    value={currentToken.postUrl || ""}
                    onChange={(e) =>
                      setCurrentToken({
                        ...currentToken,
                        postUrl: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <select
                    id="status"
                    value={currentToken.status || "not tested"}
                    onChange={(e) =>
                      setCurrentToken({
                        ...currentToken,
                        status: e.target.value as TokenRecord["status"],
                      })
                    }
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="not tested">Not Tested</option>
                    <option value="active">Active</option>
                    <option value="failed">Failed</option>
                    <option value="test again">Test Again</option>
                  </select>
                </div>
              </div>

              {/* Right side: Note */}
              <div>
                <Label htmlFor="note">Note</Label>
                <Textarea
                  id="note"
                  value={currentToken.note || ""}
                  onChange={(e) =>
                    setCurrentToken({ ...currentToken, note: e.target.value })
                  }
                  className="h-32"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center gap-2 mb-3">
                <Label>Model List</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleFetchModels}
                  disabled={
                    isFetching || !currentToken.urlPath || !currentToken.token
                  }
                >
                  {isFetching ? "Fetching..." : "Fetch Models"}
                </Button>
              </div>

              {/* Current models display */}
              {currentToken.modelList && currentToken.modelList.length > 0 && (
                <div className="mb-3 max-h-32 overflow-y-auto border rounded-md p-2 bg-gray-50">
                  <div className="flex flex-wrap gap-2">
                    {currentToken.modelList.map((model, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-1"
                      >
                        <Badge
                          variant="secondary"
                          className="text-xs"
                        >
                          {model}
                        </Badge>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0 hover:bg-red-100"
                          onClick={() => removeModel(model)}
                        >
                          <X className="h-3 w-3 text-red-500" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Add new model */}
              <div className="flex gap-2">
                <Input
                  placeholder="Add model name..."
                  value={newModelInput}
                  onChange={(e) => setNewModelInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addModel();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addModel}
                  disabled={!newModelInput.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {fetchMessage && (
                <p className="text-sm mt-2 text-gray-600">{fetchMessage}</p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddDialogOpen(false);
                setIsEditDialogOpen(false);
                setNewModelInput("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {editingTokenName ? "Updating..." : "Creating..."}
                </>
              ) : editingTokenName ? (
                "Update"
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Toast Notifications */}
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        {/* Success Toast */}
        {successMessage && (
          <div
            className="bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 transform transition-all duration-300 ease-in-out"
            style={{ animation: "slideInFromRight 0.3s ease-out" }}
          >
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">{successMessage}</p>
            </div>
            <button
              onClick={() => setSuccessMessage("")}
              className="flex-shrink-0 text-green-200 hover:text-white transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}

        {/* Error Toast */}
        {errorMessage && (
          <div
            className="bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 transform transition-all duration-300 ease-in-out"
            style={{ animation: "slideInFromRight 0.3s ease-out" }}
          >
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">{errorMessage}</p>
            </div>
            <button
              onClick={() => setErrorMessage("")}
              className="flex-shrink-0 text-red-200 hover:text-white transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Test Dialog */}
      <Dialog
        open={isTestDialogOpen}
        onOpenChange={(open) => {
          setIsTestDialogOpen(false);
          setTestResults({});
          setTestSelectedModels([]);
          setModelSearchTerm("");
          setCurrentTestingModel("");
        }}
      >
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto w-[95vw] sm:w-full">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <Globe className="w-5 h-5 text-blue-600" />
              Test API - {testingToken?.name}
              <Badge
                variant={
                  testingToken?.type === "Chat API" ? "default" : "secondary"
                }
              >
                {testingToken?.type}
              </Badge>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Token Info */}
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div>
                <Label className="font-medium">API URL:</Label>
                <p className="text-gray-600 font-mono text-sm">
                  {testingToken?.postUrl ||
                    (testingToken?.urlPath
                      ? `${testingToken.urlPath.replace(
                          /\/+$/,
                          ""
                        )}/v1/chat/completions`
                      : testingToken?.apiPath || "/api/chat")}
                </p>
              </div>
              <div>
                <Label className="font-medium">Authorization:</Label>
                <p className="text-gray-600">
                  {testingToken?.token
                    ? "Bearer token configured"
                    : "No token configured"}
                </p>
              </div>
            </div>

            {/* Model Selection */}
            <div>
              <Label className="font-medium mb-2 block">Select Models:</Label>
              {testingToken?.modelList && testingToken.modelList.length > 0 ? (
                <div className="space-y-3">
                  {/* Search Input */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search models..."
                      value={modelSearchTerm}
                      onChange={(e) => setModelSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* Selected Models Display */}
                  {testSelectedModels.length > 0 && (
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <Label className="text-sm font-medium text-blue-800 mb-2 block">
                        Selected Models ({testSelectedModels.length}):
                      </Label>
                      <div className="flex flex-wrap gap-2">
                        {testSelectedModels.map((model) => (
                          <Badge
                            key={model}
                            variant="secondary"
                            className="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200"
                            onClick={() => toggleModelSelection(model)}
                          >
                            {model}
                            <X className="w-3 h-3 ml-1" />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Model List */}
                  <div className="max-h-48 overflow-y-auto border rounded-lg">
                    {getFilteredModels().map((model) => {
                      const testStatus = getModelTestStatus(
                        testingToken!,
                        model
                      );
                      const modelResult = testingToken?.modelTestResults?.find(
                        (result) => result.modelName === model
                      );

                      return (
                        <div
                          key={model}
                          className={`flex items-center justify-between p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 ${
                            testSelectedModels.includes(model)
                              ? "bg-blue-50"
                              : ""
                          }`}
                          onClick={() => toggleModelSelection(model)}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span>{model}</span>
                              {testStatus !== undefined && (
                                <Badge
                                  variant="secondary"
                                  className={`text-xs ${
                                    testStatus === true
                                      ? "bg-green-100 text-green-800 border-green-200"
                                      : "bg-red-100 text-red-800 border-red-200"
                                  }`}
                                >
                                  {testStatus ? "Success" : "Failed"}
                                </Badge>
                              )}
                            </div>
                            {modelResult && (
                              <div className="text-xs text-gray-500 mt-1">
                                {modelResult.lastTestDuration && (
                                  <span>
                                    {(
                                      modelResult.lastTestDuration / 1000
                                    ).toFixed(2)}
                                    s
                                  </span>
                                )}
                                {modelResult.lastTestAt && (
                                  <span className="ml-2">
                                    {formatMalaysiaTime(modelResult.lastTestAt)}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                          {testSelectedModels.includes(model) && (
                            <Check className="w-4 h-4 text-blue-600" />
                          )}
                        </div>
                      );
                    })}
                    {getFilteredModels().length === 0 && (
                      <div className="p-3 text-gray-500 text-center">
                        {modelSearchTerm
                          ? "No models match your search"
                          : "No models available"}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No models available</p>
              )}
            </div>

            {/* Test Button */}
            <Button
              onClick={handleRunTest}
              disabled={isTestLoading || testSelectedModels.length === 0}
              className="w-full"
            >
              {isTestLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {currentTestingModel
                    ? `Testing ${currentTestingModel}...`
                    : "Testing..."}
                </>
              ) : (
                <>
                  <Globe className="w-4 h-4 mr-2" />
                  Test Selected Models ({testSelectedModels.length})
                </>
              )}
            </Button>

            {/* Test Results */}
            {Object.keys(testResults).length > 0 && (
              <div className="space-y-4">
                <Label className="font-medium text-lg">Test Results:</Label>
                {Object.entries(testResults).map(([model, result]) => (
                  <div
                    key={model}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 gap-2">
                      <h4 className="font-medium break-all">{model}</h4>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={result.success ? "default" : "destructive"}
                          className="w-fit"
                        >
                          {result.success ? "Success" : "Failed"}
                        </Badge>
                      </div>
                    </div>

                    {/* Response Time Information */}
                    {(result.firstByteTime !== undefined ||
                      result.totalTime !== undefined) && (
                      <div className="bg-gray-50 p-3 rounded mb-3 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="font-medium text-gray-700">
                            First Byte Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.firstByteTime !== undefined
                              ? `${(result.firstByteTime / 1000).toFixed(2)}s`
                              : "N/A"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium text-gray-700">
                            Total Response Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.totalTime !== undefined
                              ? `${(result.totalTime / 1000).toFixed(2)}s`
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    )}

                    {result.success ? (
                      <Textarea
                        value={result.response}
                        readOnly
                        className="min-h-[150px] font-mono text-sm text-green-600 border-green-300"
                      />
                    ) : (
                      <div className="bg-red-50 p-3 rounded border border-red-200">
                        <p className="text-red-600 text-sm font-mono">
                          {result.error}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsTestDialogOpen(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              Confirm Delete Token
            </AlertDialogTitle>
            <AlertDialogDescription>
              <p>
                Are you sure you want to delete token{" "}
                <span className="font-semibold text-red-600">
                  "{tokenToDelete}"
                </span>
                ?
              </p>
              <p className="text-sm text-gray-600 mt-2">
                This action cannot be undone. Please confirm your choice.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
